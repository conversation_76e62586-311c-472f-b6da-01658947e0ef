import { Pool, PoolConfig } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Enhanced logging for database operations
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[DB-INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[DB-WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[DB-ERROR] ${message}`, ...args),
  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[DB-DEBUG] ${message}`, ...args);
    }
  }
};

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl: boolean;
  poolMin: number;
  poolMax: number;
  connectionString?: string;
}

export function getDatabaseConfig(): DatabaseConfig {
  try {
    // Validate required environment variables
    const requiredVars = ['DATABASE_HOST', 'DATABASE_NAME', 'DATABASE_USER'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0 && !process.env.DATABASE_URL) {
      logger.error('Missing required database environment variables:', missingVars);
      logger.info('Please set these variables in your .env file or provide DATABASE_URL');
      throw new Error(`Missing database configuration: ${missingVars.join(', ')}`);
    }

    const config: DatabaseConfig = {
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT || '5432'),
      database: process.env.DATABASE_NAME || 'procure_agent',
      user: process.env.DATABASE_USER || 'postgres',
      password: process.env.DATABASE_PASSWORD || '',
      ssl: process.env.DATABASE_SSL === 'true',
      poolMin: parseInt(process.env.DATABASE_POOL_MIN || '2'),
      poolMax: parseInt(process.env.DATABASE_POOL_MAX || '10'),
    };

    // Use DATABASE_URL if provided (common in production environments)
    if (process.env.DATABASE_URL) {
      config.connectionString = process.env.DATABASE_URL;
      logger.debug('Using DATABASE_URL for connection');
    } else {
      logger.debug('Using individual database config variables');
    }

    // Validate port number
    if (isNaN(config.port) || config.port < 1 || config.port > 65535) {
      throw new Error(`Invalid database port: ${process.env.DATABASE_PORT}`);
    }

    // Validate pool settings
    if (config.poolMin < 0 || config.poolMax < config.poolMin) {
      throw new Error(`Invalid pool configuration: min=${config.poolMin}, max=${config.poolMax}`);
    }

    logger.debug('Database configuration validated successfully');
    return config;
  } catch (error) {
    logger.error('Failed to get database configuration:', error);
    throw error;
  }
}

export function createPoolConfig(): PoolConfig {
  const config = getDatabaseConfig();
  
  const poolConfig: PoolConfig = {
    min: config.poolMin,
    max: config.poolMax,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  };

  if (config.connectionString) {
    poolConfig.connectionString = config.connectionString;
  } else {
    poolConfig.host = config.host;
    poolConfig.port = config.port;
    poolConfig.database = config.database;
    poolConfig.user = config.user;
    poolConfig.password = config.password;
    poolConfig.ssl = config.ssl;
  }

  return poolConfig;
}

// Singleton database pool
let pool: Pool | null = null;

export function getPool(): Pool {
  if (!pool) {
    try {
      const poolConfig = createPoolConfig();
      pool = new Pool(poolConfig);

      // Enhanced error handling for pool
      pool.on('error', (err) => {
        logger.error('Unexpected error on idle client:', err);
        logger.error('Pool will be recreated on next request');
        pool = null; // Reset pool to allow recreation
      });

      // Connection event handlers
      pool.on('connect', (client) => {
        logger.debug('New client connected to database');
      });

      pool.on('acquire', (client) => {
        logger.debug('Client acquired from pool');
      });

      pool.on('remove', (client) => {
        logger.debug('Client removed from pool');
      });

      logger.info('🗄️ Database pool created successfully');
    } catch (error) {
      logger.error('Failed to create database pool:', error);
      throw new Error(`Database pool creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return pool;
}

export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('🗄️ Database pool closed');
  }
}

// Test database connection with enhanced error reporting
export async function testConnection(): Promise<boolean> {
  try {
    logger.info('Testing database connection...');
    const pool = getPool();
    const client = await pool.connect();

    // Test basic query with timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Connection test timeout')), 5000);
    });

    const queryPromise = client.query('SELECT NOW() as current_time, version() as pg_version');
    const result = await Promise.race([queryPromise, timeoutPromise]);

    client.release();

    logger.info('✅ Database connection successful');
    logger.debug('Database info:', {
      currentTime: result.rows[0]?.current_time,
      version: result.rows[0]?.pg_version?.split(' ')[0] // Just the version number
    });

    return true;
  } catch (error) {
    logger.error('❌ Database connection failed:', error);

    // Provide specific error guidance
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        logger.error('💡 PostgreSQL server is not running or not accessible');
      } else if (error.message.includes('authentication')) {
        logger.error('💡 Check your database credentials in .env file');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        logger.error('💡 Database does not exist. Create it first or check DATABASE_NAME');
      } else if (error.message.includes('timeout')) {
        logger.error('💡 Database connection timeout. Check network connectivity');
      }
    }

    return false;
  }
}

// Check if pgvector extension is available
export async function checkPgVectorExtension(): Promise<boolean> {
  try {
    const pool = getPool();
    const client = await pool.connect();
    
    // Check if pgvector extension exists
    const result = await client.query(`
      SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'vector'
      ) as has_vector;
    `);
    
    client.release();
    
    const hasVector = result.rows[0]?.has_vector || false;
    
    if (hasVector) {
      console.log('✅ pgvector extension is available');
    } else {
      console.warn('⚠️ pgvector extension is not installed');
    }
    
    return hasVector;
  } catch (error) {
    console.error('❌ Failed to check pgvector extension:', error);
    return false;
  }
}
