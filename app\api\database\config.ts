import { Pool, PoolConfig } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl: boolean;
  poolMin: number;
  poolMax: number;
  connectionString?: string;
}

export function getDatabaseConfig(): DatabaseConfig {
  const config: DatabaseConfig = {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    database: process.env.DATABASE_NAME || 'procure_agent',
    user: process.env.DATABASE_USER || 'postgres',
    password: process.env.DATABASE_PASSWORD || '',
    ssl: process.env.DATABASE_SSL === 'true',
    poolMin: parseInt(process.env.DATABASE_POOL_MIN || '2'),
    poolMax: parseInt(process.env.DATABASE_POOL_MAX || '10'),
  };

  // Use DATABASE_URL if provided (common in production environments)
  if (process.env.DATABASE_URL) {
    config.connectionString = process.env.DATABASE_URL;
  }

  return config;
}

export function createPoolConfig(): PoolConfig {
  const config = getDatabaseConfig();
  
  const poolConfig: PoolConfig = {
    min: config.poolMin,
    max: config.poolMax,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  };

  if (config.connectionString) {
    poolConfig.connectionString = config.connectionString;
  } else {
    poolConfig.host = config.host;
    poolConfig.port = config.port;
    poolConfig.database = config.database;
    poolConfig.user = config.user;
    poolConfig.password = config.password;
    poolConfig.ssl = config.ssl;
  }

  return poolConfig;
}

// Singleton database pool
let pool: Pool | null = null;

export function getPool(): Pool {
  if (!pool) {
    const poolConfig = createPoolConfig();
    pool = new Pool(poolConfig);
    
    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
      process.exit(-1);
    });

    // Log pool connection
    console.log('🗄️ Database pool created');
  }
  
  return pool;
}

export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('🗄️ Database pool closed');
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const pool = getPool();
    const client = await pool.connect();
    
    // Test basic query
    const result = await client.query('SELECT NOW()');
    client.release();
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Check if pgvector extension is available
export async function checkPgVectorExtension(): Promise<boolean> {
  try {
    const pool = getPool();
    const client = await pool.connect();
    
    // Check if pgvector extension exists
    const result = await client.query(`
      SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'vector'
      ) as has_vector;
    `);
    
    client.release();
    
    const hasVector = result.rows[0]?.has_vector || false;
    
    if (hasVector) {
      console.log('✅ pgvector extension is available');
    } else {
      console.warn('⚠️ pgvector extension is not installed');
    }
    
    return hasVector;
  } catch (error) {
    console.error('❌ Failed to check pgvector extension:', error);
    return false;
  }
}
